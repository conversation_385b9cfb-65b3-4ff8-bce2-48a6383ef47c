// utils/debounce.js
export function useDebounce(fn, delay = 500) {
  let timer = null

  return function(...args) {
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => {
      fn.apply(this, args)
    }, delay)
  }
}

// 如果需要取消功能
export function useDebounceWithCancel(fn, delay = 500) {
  let timer = null

  const debouncedFn = function(...args) {
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => {
      fn.apply(this, args)
    }, delay)
  }

  debouncedFn.cancel = () => {
    if (timer) {
      clearTimeout(timer)
      timer = null
    }
  }

  return debouncedFn
}