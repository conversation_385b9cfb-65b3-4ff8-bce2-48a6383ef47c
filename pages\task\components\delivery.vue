<script setup>
  import { ref, onMounted } from 'vue'
  import taskApi from '@/apis/task'

  // 在途任务列表
  const deliveryList = ref([])
  // 下一页页码
  const nextPage = ref(1)
  // 每页数据条数
  const pageSize = ref(5)
  // 在途列任务列表是否为空
  const isEmpty = ref(false)
  // 是否还有更多数据
  const hasMore = ref(true)
  // 是否加载完成
  const isTriggered = ref(false)

  // 生命周期（获取数据）
  onMounted(() => {
    getDeliveryList()
  })

  // 监听页面是否滚动到底部
  function onScrollToLower() {
    // 没有更多数据时则不需要再请求了
    if (!hasMore.value) return
    // 获取下一页数据
    getDeliveryList(nextPage.value)
  }

  // 监听用户的下拉操作
  async function onScrollViewRefresh() {
    isTriggered.value = true
    await getDeliveryList()
    isTriggered.value = false
  }

  // 在途任务列表
  async function getDeliveryList(page = 1, pageSize = 10) {
    const { code, data } = await taskApi.list(2, page, pageSize)
    if (code !== 200) return uni.utils.toast('在途任务获取失败！')
    // 页面为 1 时，清空数组
    if (page === 1) deliveryList.value = []
    // 渲染数据
    deliveryList.value = [...deliveryList.value, ...(data.items || [])]
    // 更新下一页页码
    nextPage.value = ++data.page
    // 判断列表是否为空
    isEmpty.value = deliveryList.value.length === 0
    // 判断是否还有更多数据
    hasMore.value = data.page < data.totalPage
  }
</script>
<template>
  <scroll-view
    scroll-y
    @scrolltolower="onScrollToLower"
    @refresherrefresh="onScrollViewRefresh"
    :refresher-triggered="isTriggered"
    refresher-enabled
    class="scroll-view"
  >
    <view class="scroll-view-wrapper">
      <view
        v-for="delivery in deliveryList"
        :key="delivery.id"
        class="task-card"
      >
        <navigator
          hover-class="none"
          :url="`/subpkg_task/detail/index?id=${delivery.id}`"
        >
          <view class="header">
            <text class="no">任务编号: {{ delivery.transportTaskId }}</text>
          </view>
          <view class="body">
            <view class="timeline">
              <view class="line">{{ delivery.startAddress }}</view>
              <view class="line">{{ delivery.endAddress }}</view>
            </view>
          </view>
        </navigator>
        <view class="footer">
          <view class="label">到货时间</view>
          <view class="time">{{ delivery.planArrivalTime }}</view>
          <navigator
            v-if="delivery.status === 2"
            hover-class="none"
            :url="`/subpkg_task/delivery/index?id=${delivery.id}`"
            class="action"
          >
            交付
          </navigator>
          <navigator
            v-if="delivery.status === 4"
            hover-class="none"
            :url="`/subpkg_task/record/index?transportTaskId=${delivery.transportTaskId}&actualDepartureTime=${delivery.actualDepartureTime}`"
            class="action"
          >
            回车登记
          </navigator>
        </view>
      </view>
      <view v-if="isEmpty" class="task-blank">无在途货物</view>
    </view>
  </scroll-view>
</template>
<style lang="scss" scoped>
  @import './styles.scss';
</style>
